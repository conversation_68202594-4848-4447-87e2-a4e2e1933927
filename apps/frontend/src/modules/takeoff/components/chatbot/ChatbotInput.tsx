'use client';

import React, { useRef } from 'react';
import { ArrowUp, Square } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { cn } from '@/lib/utils';
import { AIModel, AI_MODEL_OPTIONS } from './types/chatbot-types';

interface ChatbotInputProps {
  input: string;
  handleInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  handleSubmit: (e: React.FormEvent) => void;
  isLoading: boolean;
  disabled?: boolean;
  selectedModel: AIModel;
  onModelChange: (model: AIModel) => void;
  onStop?: () => void;
  isStreaming?: boolean;
}

export const ChatbotInput: React.FC<ChatbotInputProps> = ({
  input,
  handleInputChange,
  handleSubmit,
  isLoading,
  disabled = false,
  selectedModel,
  onModelChange,
  onStop,
  isStreaming = false,
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (input.trim() && !isLoading && !disabled) {
        handleSubmit(e);
      }
    }
  };

  const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    handleInputChange(e);

    // Auto-resize textarea
    const textarea = e.target;
    textarea.style.height = 'auto';
    textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`;
  };

  const handleButtonClick = (e: React.FormEvent) => {
    if (isStreaming && onStop) {
      onStop();
    } else {
      handleSubmit(e);
    }
  };

  const isSubmitDisabled = !input.trim() || isLoading || disabled;
  const showStopButton = isStreaming && onStop;

  return (
    <div className="px-3 pb-3 bg-background">
      <form onSubmit={handleButtonClick}>
        {/* Big container matching the UI */}
        <div className="bg-white border border-gray-200 rounded-3xl p-4 shadow-sm hover:shadow-md transition-all duration-200">
          {/* Input area - further reduced height */}
          <div className="mb-3">
            <Textarea
              ref={textareaRef}
              value={input}
              onChange={handleTextareaChange}
              onKeyDown={handleKeyDown}
              placeholder="Ask ai anything about the drawing"
              disabled={isLoading || disabled}
              className={cn(
                'min-h-[18px] max-h-[120px] resize-none text-base border-0 p-0',
                'focus-visible:ring-0 focus-visible:ring-offset-0 shadow-none',
                'placeholder:text-gray-400 bg-transparent w-full',
                'scrollbar-thin scrollbar-thumb-gray-200 scrollbar-track-transparent',
              )}
              rows={1}
            />
          </div>

          {/* Bottom row: Model selector left, Submit button right */}
          <div className="flex items-center justify-between">
            {/* Model selector on the left - compact size */}
            <div className="flex-shrink-0">
              <Select value={selectedModel} onValueChange={onModelChange}>
                <SelectTrigger className="w-[110px] h-8 text-xs border-0 bg-gray-50 hover:bg-gray-100 rounded-lg shadow-none focus:ring-0 focus:ring-offset-0">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="rounded-lg border shadow-lg min-w-[130px]">
                  {AI_MODEL_OPTIONS.map((option) => (
                    <SelectItem
                      key={option.value}
                      value={option.value}
                      className="text-xs rounded focus:bg-primary/10"
                    >
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Submit button on the right - fully round and small */}
            <div className="flex-shrink-0">
              <Button
                type={showStopButton ? 'button' : 'submit'}
                size="icon"
                disabled={showStopButton ? false : isSubmitDisabled}
                className={cn(
                  'h-10 w-10 rounded-full transition-all duration-200',
                  showStopButton
                    ? 'bg-red-500 hover:bg-red-500/90 text-destructive-foreground shadow-sm hover:shadow-md'
                    : isSubmitDisabled
                      ? 'bg-gray-200 text-gray-400 cursor-not-allowed hover:bg-gray-200'
                      : 'bg-primary hover:bg-primary/90 text-primary-foreground shadow-sm hover:shadow-md',
                )}
              >
                {showStopButton ? (
                  <Square className="h-4 w-4" />
                ) : (
                  <ArrowUp className="h-5 w-5" />
                )}
              </Button>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
};
